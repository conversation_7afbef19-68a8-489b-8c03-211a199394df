import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Req,
  Get,
  Logger,
  UseInterceptors,
} from '@nestjs/common';
import { Request } from 'express';
import { Web3AuthService } from './web3-auth.service';
import { GetNonceDto } from './dto/get-nonce.dto';
import { TranslationService } from '../common/services/translation.service';
import { Web3AuthValidationPipe } from './pipes/web3-auth-validation.pipe';
import { SkipResponseTransform } from './decorators/skip-response-transform.decorator';

@Controller('web3-auth')
export class Web3AuthController {
  private readonly logger = new Logger(Web3AuthController.name);

  constructor(
    private readonly web3AuthService: Web3AuthService,
    private readonly translationService: TranslationService,
  ) {}

  /**
   * Redis连接健康检查
   * 路径: /api/web3-auth/health
   * 方法: GET
   */
  @Get('health')
  @SkipResponseTransform()
  async healthCheck() {
    try {
      const isRedisHealthy = await this.web3AuthService.checkRedisConnection();

      return {
        ok: true,
        data: {
          redis: isRedisHealthy ? 'connected' : 'disconnected',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error: any) {
      throw new HttpException(
        {
          ok: false,
          message: 'Health check failed',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取用于签名的nonce
   * 路径: /api/web3-auth/nonce
   * 方法: POST
   */
  @Post('nonce')
  @SkipResponseTransform()
  async getNonce(
    @Body(Web3AuthValidationPipe) getNonceDto: GetNonceDto,
    @Req() req: Request,
  ) {
    const startTime = Date.now();
    const clientIP = req.ip || req.socket?.remoteAddress || 'unknown';

    try {
      this.logger.debug(`Nonce请求开始，钱包: ${getNonceDto.walletAddress.substring(0, 10)}..., IP: ${clientIP}`);

      const result = await this.web3AuthService.getNonce(getNonceDto);

      const duration = Date.now() - startTime;
      this.logger.log(`Nonce请求成功，钱包: ${getNonceDto.walletAddress.substring(0, 10)}..., 耗时: ${duration}ms`);

      // 使用与原版本相同的响应格式
      return {
        ok: true,
        data: result,
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.logger.error(`获取nonce失败，钱包: ${getNonceDto.walletAddress?.substring(0, 10) || 'unknown'}..., 耗时: ${duration}ms`, error);

      // 根据错误类型返回不同的错误消息
      let errorMessage: string;
      let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;

      if (error.message === 'Invalid wallet address format') {
        errorMessage = this.translationService.tFromRequest(
          req,
          'errors.invalidAddress'
        );
        statusCode = HttpStatus.BAD_REQUEST;
      } else if (error.message === 'Failed to store nonce in Redis') {
        errorMessage = this.translationService.tFromRequest(
          req,
          'errors.serverError'
        );
        statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      } else {
        errorMessage = this.translationService.tFromRequest(
          req,
          'errors.serverError'
        );
      }

      throw new HttpException(
        {
          ok: false,
          message: errorMessage,
          error: error.message,
        },
        statusCode,
      );
    }
  }
}
