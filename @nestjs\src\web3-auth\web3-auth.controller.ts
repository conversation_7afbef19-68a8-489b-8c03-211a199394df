import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Req,
  UsePipes,
} from '@nestjs/common';
import { Request } from 'express';
import { Web3AuthService } from './web3-auth.service';
import { GetNonceDto, NonceResponseDto } from './dto/get-nonce.dto';
import { TranslationService } from '../common/services/translation.service';
import { Web3AuthValidationPipe } from './pipes/web3-auth-validation.pipe';

@Controller('api/web3-auth')
export class Web3AuthController {
  constructor(
    private readonly web3AuthService: Web3AuthService,
    private readonly translationService: TranslationService,
    private readonly validationPipe: Web3AuthValidationPipe,
  ) {}

  /**
   * 获取用于签名的nonce
   * 路径: /api/web3-auth/nonce
   * 方法: POST
   */
  @Post('nonce')
  async getNonce(
    @Body(Web3AuthValidationPipe) getNonceDto: GetNonceDto,
    @Req() req: Request,
  ) {
    try {
      const result = await this.web3AuthService.getNonce(getNonceDto);

      // 使用与原版本相同的响应格式
      return {
        ok: true,
        data: result,
      };
    } catch (error: any) {
      console.error('获取nonce失败:', error);

      const errorMessage = this.translationService.tFromRequest(
        req,
        'errors.serverError'
      );

      throw new HttpException(
        {
          ok: false,
          message: errorMessage,
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
