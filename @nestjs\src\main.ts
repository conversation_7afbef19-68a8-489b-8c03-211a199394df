import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';
import { I18nLanguageInterceptor } from './i18n/interceptors/i18n-language.interceptor';
import { CustomI18nService } from './i18n/i18n.service';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 获取应用配置
  const appConfig = configService.get('app');

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // 获取服务实例
  const i18nService = app.get(CustomI18nService);
  const i18nLanguageInterceptor = app.get(I18nLanguageInterceptor);

  // 全局异常过滤器
  app.useGlobalFilters(new HttpExceptionFilter(i18nService));

  // 全局响应拦截器
  app.useGlobalInterceptors(new ResponseInterceptor());

  // 全局i18n语言拦截器
  app.useGlobalInterceptors(i18nLanguageInterceptor);

  // CORS 配置
  app.enableCors({
    origin: appConfig.cors.origin,
    methods: appConfig.cors.methods,
    allowedHeaders: appConfig.cors.allowedHeaders,
    credentials: true,
  });

  // API 前缀
  app.setGlobalPrefix(appConfig.api.prefix);

  // Swagger 文档配置
  const config = new DocumentBuilder()
    .setTitle(appConfig.api.title)
    .setDescription(appConfig.api.description)
    .setVersion(appConfig.api.version)
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup(`${appConfig.api.prefix}/docs`, app, document);

  const port = appConfig.port;
  await app.listen(port);

  console.log(`NestJS application is running on: http://localhost:${port}`);
  console.log(`Swagger documentation available at: http://localhost:${port}/${appConfig.api.prefix}/docs`);
}

bootstrap();
