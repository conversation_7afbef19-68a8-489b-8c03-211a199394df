import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { TranslationService } from '../../common/services/translation.service';

@Injectable()
export class Web3AuthValidationPipe implements PipeTransform<any> {
  constructor(
    private readonly translationService: TranslationService,
    @Inject(REQUEST) private readonly request: Request,
  ) {}

  async transform(value: any, { metatype }: ArgumentMetadata) {
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }

    const object = plainToClass(metatype, value);
    const errors = await validate(object);

    if (errors.length > 0) {
      // 格式化验证错误，与原版本AJV格式保持一致
      const formattedErrors = errors.map(error => ({
        field: error.property,
        message: Object.values(error.constraints || {})[0] || 'Validation failed',
      }));

      // 获取翻译后的参数验证失败消息
      const errorMessage = this.translationService.tFromRequest(
        this.request,
        'errors.paramValidation'
      );

      // 抛出与原版本格式完全一致的错误
      throw new BadRequestException({
        ok: false,
        message: errorMessage,
        error: formattedErrors,
      });
    }

    return value;
  }

  private toValidate(metatype: Function): boolean {
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }
}
