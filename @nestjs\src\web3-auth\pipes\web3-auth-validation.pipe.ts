import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
} from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { TranslationService } from '../../common/services/translation.service';

@Injectable()
export class Web3AuthValidationPipe implements PipeTransform<any> {
  constructor(private readonly translationService: TranslationService) {}

  async transform(value: any, { metatype }: ArgumentMetadata) {
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }

    const object = plainToClass(metatype, value);
    const errors = await validate(object);

    if (errors.length > 0) {
      // 格式化验证错误，与原版本保持一致
      const formattedErrors = errors.map(error => ({
        field: error.property,
        message: Object.values(error.constraints || {})[0] || 'Validation failed',
      }));

      // 抛出与原版本格式一致的错误
      throw new BadRequestException({
        ok: false,
        message: 'Parameter validation failed',
        error: formattedErrors,
      });
    }

    return value;
  }

  private toValidate(metatype: Function): boolean {
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }
}
