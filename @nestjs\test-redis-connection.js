// Redis连接测试脚本
const axios = require('axios');

async function testRedisConnection() {
  console.log('🔍 测试Redis连接和Web3Auth nonce存储...\n');

  try {
    // 0. 首先测试健康检查
    console.log('0. 测试Redis健康检查...');
    const healthResponse = await axios.get('http://localhost:3001/api/web3-auth/health');

    if (healthResponse.data.ok && healthResponse.data.data.redis === 'connected') {
      console.log('✅ Redis健康检查通过');
      console.log(`   状态: ${healthResponse.data.data.redis}`);
      console.log(`   时间: ${healthResponse.data.data.timestamp}`);
    } else {
      console.log('❌ Redis健康检查失败');
      console.log('响应:', JSON.stringify(healthResponse.data, null, 2));
      return;
    }

    // 1. 测试获取nonce
    console.log('1. 测试获取nonce...');
    const walletAddress = '******************************************';
    
    const response1 = await axios.post('http://localhost:3001/api/web3-auth/nonce', {
      walletAddress: walletAddress
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'en'
      }
    });
    
    if (response1.data.ok && response1.data.data.nonce) {
      console.log('✅ 第一次获取nonce成功');
      console.log(`   Nonce: ${response1.data.data.nonce}`);
      
      const firstNonce = response1.data.data.nonce;
      
      // 2. 立即再次获取nonce，应该得到新的nonce（因为每次都生成新的）
      console.log('\n2. 测试再次获取nonce...');
      const response2 = await axios.post('http://localhost:3001/api/web3-auth/nonce', {
        walletAddress: walletAddress
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': 'en'
        }
      });
      
      if (response2.data.ok && response2.data.data.nonce) {
        console.log('✅ 第二次获取nonce成功');
        console.log(`   Nonce: ${response2.data.data.nonce}`);
        
        const secondNonce = response2.data.data.nonce;
        
        if (firstNonce !== secondNonce) {
          console.log('✅ 每次生成的nonce都不同，符合预期');
        } else {
          console.log('⚠️  两次生成的nonce相同，可能有问题');
        }
        
        // 3. 测试不同钱包地址
        console.log('\n3. 测试不同钱包地址...');
        const differentWallet = '******************************************';
        
        const response3 = await axios.post('http://localhost:3001/api/web3-auth/nonce', {
          walletAddress: differentWallet
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Accept-Language': 'en'
          }
        });
        
        if (response3.data.ok && response3.data.data.nonce) {
          console.log('✅ 不同钱包地址获取nonce成功');
          console.log(`   Nonce: ${response3.data.data.nonce}`);
          
          // 4. 验证消息格式
          console.log('\n4. 验证签名消息格式...');
          const message = response3.data.data.message;
          
          if (message.includes('Welcome to MooFun!') && 
              message.includes('Security code:') && 
              message.includes('Timestamp:')) {
            console.log('✅ 签名消息格式正确');
            console.log(`   消息预览: ${message.substring(0, 100)}...`);
          } else {
            console.log('❌ 签名消息格式不正确');
            console.log(`   实际消息: ${message}`);
          }
          
        } else {
          console.log('❌ 不同钱包地址获取nonce失败');
        }
        
      } else {
        console.log('❌ 第二次获取nonce失败');
      }
      
    } else {
      console.log('❌ 第一次获取nonce失败');
      console.log('响应:', JSON.stringify(response1.data, null, 2));
    }
    
    // 5. 测试Redis过期时间（这个测试需要等待，所以只是提示）
    console.log('\n5. Redis过期时间测试');
    console.log('ℹ️  Nonce在Redis中设置了5分钟过期时间');
    console.log('ℹ️  可以通过Redis CLI验证: redis-cli TTL web3_auth_nonce:' + walletAddress.toLowerCase());
    
  } catch (error) {
    console.log('❌ Redis连接测试失败:');
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('错误:', error.message);
    }
  }
  
  console.log('\n🎉 Redis连接测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  testRedisConnection().catch(console.error);
}

module.exports = { testRedisConnection };
