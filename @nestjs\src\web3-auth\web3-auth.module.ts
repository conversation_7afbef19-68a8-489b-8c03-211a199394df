import { Module } from '@nestjs/common';
import { Web3AuthController } from './web3-auth.controller';
import { Web3AuthService } from './web3-auth.service';
import { Web3AuthValidationPipe } from './pipes/web3-auth-validation.pipe';
import { RedisModule } from '../redis/redis.module';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    RedisModule,
    CommonModule,
  ],
  controllers: [Web3AuthController],
  providers: [Web3AuthService, Web3AuthValidationPipe],
  exports: [Web3AuthService],
})
export class Web3AuthModule {}
