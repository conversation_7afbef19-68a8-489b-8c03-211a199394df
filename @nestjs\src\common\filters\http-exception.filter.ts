import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { CustomI18nService } from '../../i18n/i18n.service';
import { I18nException } from '../exceptions/i18n.exception';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(private readonly i18nService: CustomI18nService) {}

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();

    // 获取请求语言
    const language = this.i18nService.findBestMatchingLanguage(request.language || 'en');

    // 处理 I18n 异常
    if (exception instanceof I18nException) {
      const exceptionResponse = exception.getResponse() as any;
      const translatedMessage = this.i18nService.translate(
        exceptionResponse.messageKey,
        exceptionResponse.variables,
        language,
      );

      const errorResponse = {
        success: false,
        statusCode: status,
        timestamp: new Date().toISOString(),
        path: request.url,
        method: request.method,
        message: translatedMessage,
        language,
        messageKey: exceptionResponse.messageKey,
      };

      return response.status(status).json(errorResponse);
    }

    // 翻译错误消息
    let message = exception.message || 'Internal server error';
    let details = null;

    // 处理验证错误
    const exceptionResponse = exception.getResponse();
    if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
      const responseObj = exceptionResponse as any;

      if (responseObj.errors && Array.isArray(responseObj.errors)) {
        // 翻译验证错误
        details = responseObj.errors.map((error: any) => ({
          ...error,
          message: this.i18nService.translate(`validation.${error.constraint}`, error, language) || error.message,
        }));
        message = this.i18nService.translate('common.validation_failed', {}, language);
      }
    }

    if (!details) {
      try {
        // 尝试翻译常见的HTTP错误
        switch (status) {
          case HttpStatus.UNAUTHORIZED:
            message = this.i18nService.translate('common.unauthorized', {}, language);
            break;
          case HttpStatus.FORBIDDEN:
            message = this.i18nService.translate('common.forbidden', {}, language);
            break;
          case HttpStatus.NOT_FOUND:
            message = this.i18nService.translate('common.not_found', {}, language);
            break;
          case HttpStatus.BAD_REQUEST:
            message = this.i18nService.translate('common.invalid_request', {}, language);
            break;
          case HttpStatus.INTERNAL_SERVER_ERROR:
            message = this.i18nService.translate('common.server_error', {}, language);
            break;
          default:
            // 尝试翻译自定义错误消息
            message = this.i18nService.processErrorMessage(exception.message, language);
        }
      } catch (error) {
        // 翻译失败时使用原始消息
        console.warn('Failed to translate error message:', error);
      }
    }

    const errorResponse = {
      success: false,
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      message,
      language,
      ...(details && { errors: details }),
    };

    response.status(status).json(errorResponse);
  }
}
