#!/usr/bin/env node

/**
 * I18n 功能测试脚本
 * 用于测试多语言功能是否正常工作
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

// 测试用例 - 只使用 Accept-Language 请求头
const testCases = [
  {
    name: '英语测试',
    headers: { 'Accept-Language': 'en-US,en;q=0.9' },
    expectedLang: 'en'
  },
  {
    name: '中文简体测试',
    headers: { 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8' },
    expectedLang: 'zh'
  },
  {
    name: '中文繁体测试',
    headers: { 'Accept-Language': 'zh-TW,zh-tw;q=0.9,zh;q=0.8,en;q=0.7' },
    expectedLang: 'zh-tw'
  },
  {
    name: '日语测试',
    headers: { 'Accept-Language': 'ja-<PERSON>,ja;q=0.9,en;q=0.8' },
    expectedLang: 'ja'
  },
  {
    name: '多语言优先级测试',
    headers: { 'Accept-Language': 'fr;q=0.9,zh;q=0.8,en;q=0.7' },
    expectedLang: 'zh' // 法语不支持，选择中文
  },
  {
    name: '默认语言测试（无语言头）',
    headers: {},
    expectedLang: 'en'
  },
  {
    name: '不支持的语言测试',
    headers: { 'Accept-Language': 'fr-FR,de-DE;q=0.9' },
    expectedLang: 'en' // 都不支持，使用默认语言
  }
];

async function runTests() {
  console.log('🚀 开始 I18n 功能测试...\n');

  for (const testCase of testCases) {
    console.log(`📋 ${testCase.name}`);
    
    try {
      // 测试健康检查接口
      const healthResponse = await axios.get(`${BASE_URL}/health`, {
        headers: testCase.headers
      });

      console.log(`   语言: ${healthResponse.data.language}`);
      console.log(`   状态消息: ${healthResponse.data.statusMessage}`);
      
      if (healthResponse.data.language === testCase.expectedLang) {
        console.log('   ✅ 语言检测正确');
      } else {
        console.log(`   ❌ 语言检测错误，期望: ${testCase.expectedLang}, 实际: ${healthResponse.data.language}`);
      }

      // 测试i18n测试接口
      const i18nResponse = await axios.get(`${BASE_URL}/i18n-test`, {
        headers: testCase.headers
      });

      console.log(`   翻译示例:`);
      console.log(`     成功: ${i18nResponse.data.translations['common.success']}`);
      console.log(`     错误: ${i18nResponse.data.translations['common.error']}`);
      console.log(`     每日签到: ${i18nResponse.data.translations['tasks.dailySignin']}`);

      // 测试验证错误
      try {
        await axios.post(`${BASE_URL}/test-validation`, {
          name: '', // 无效数据
          email: 'invalid-email'
        }, {
          headers: testCase.headers
        });
      } catch (validationError) {
        if (validationError.response && validationError.response.status === 400) {
          console.log(`   验证错误测试: ✅ 正确返回400错误`);
          console.log(`     错误消息: ${validationError.response.data.message}`);
        }
      }

      // 测试i18n异常
      try {
        await axios.get(`${BASE_URL}/test-error`, {
          headers: testCase.headers
        });
      } catch (errorTest) {
        if (errorTest.response && errorTest.response.status === 400) {
          console.log(`   I18n异常测试: ✅ 正确处理异常`);
          console.log(`     错误消息: ${errorTest.response.data.message}`);
        }
      }

    } catch (error) {
      console.log(`   ❌ 测试失败: ${error.message}`);
    }
    
    console.log('');
  }

  console.log('🎉 I18n 功能测试完成！');
}

// 检查服务器是否运行
async function checkServer() {
  try {
    await axios.get(`${BASE_URL}/health`);
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('🔧 简化的语言检测测试 - 只使用 Accept-Language 请求头\n');

  const isServerRunning = await checkServer();

  if (!isServerRunning) {
    console.log('❌ 服务器未运行，请先启动 NestJS 应用:');
    console.log('   cd @nestjs');
    console.log('   npm run start:dev');
    process.exit(1);
  }

  await runTests();
}

main().catch(console.error);
