import { Module, Global, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { LanguageMiddleware } from './middleware/language.middleware';
import { ResponseInterceptor } from './interceptors/response.interceptor';
import { HttpExceptionFilter } from './filters/http-exception.filter';
import { LoggingService } from './services/logging.service';
import { TranslationService } from './services/translation.service';
import { RequestContextService } from './services/request-context.service';
import { CustomI18nService } from '../i18n/i18n.service';

@Global()
@Module({
  providers: [
    LanguageMiddleware,
    ResponseInterceptor,
    HttpExceptionFilter,
    LoggingService,
    TranslationService,
    RequestContextService,
  ],
  exports: [
    LanguageMiddleware,
    ResponseInterceptor,
    HttpExceptionFilter,
    LoggingService,
    TranslationService,
    RequestContextService,
  ],
})
export class CommonModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(LanguageMiddleware)
      .forRoutes('*'); // 应用到所有路由
  }
}
