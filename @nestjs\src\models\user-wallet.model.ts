import { Table, Column, Model, DataType, BelongsTo, ForeignKey, HasMany } from 'sequelize-typescript';
import { User } from './user.model';

@Table({
  tableName: 'user_wallets',
  timestamps: true,
  charset: 'utf8mb4',
  collate: 'utf8mb4_unicode_ci',
})
export class UserWallet extends Model<UserWallet> {
  @Column({
    type: DataType.INTEGER.UNSIGNED,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER.UNSIGNED,
    allowNull: false,
  })
  userId: number;

  @Column(DataType.STRING)
  code?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  walletAddress?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  parsedWalletAddress?: string;

  // TON 相关字段（已废弃，但保留兼容性）
  @Column({
    type: DataType.DECIMAL(65, 3),
    allowNull: true,
    defaultValue: null,
  })
  ton?: number | string;

  @Column({
    type: DataType.FLOAT,
    defaultValue: 1,
  })
  winRate?: number;

  @Column({
    type: DataType.DECIMAL(65, 3),
    defaultValue: 0,
  })
  gem?: number | string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  ticket?: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  free_ticket?: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  fragment_green?: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  fragment_blue?: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  fragment_purple?: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  fragment_gold?: number;

  @Column({
    type: DataType.DECIMAL(65, 3),
    defaultValue: 0,
  })
  usd?: number;

  @Column({
    type: DataType.DECIMAL(65, 3),
    defaultValue: 0,
  })
  moof?: number;

  @Column({
    type: DataType.DECIMAL(65, 3),
    defaultValue: 0,
  })
  unlockMoof?: number;

  @Column({
    type: DataType.DECIMAL(65, 3),
    defaultValue: 0,
  })
  bullReward?: number;

  @Column({
    type: DataType.INTEGER.UNSIGNED,
    allowNull: true,
  })
  referrerWalletId?: number;

  @Column({
    type: DataType.INTEGER.UNSIGNED,
    defaultValue: 0,
  })
  referralCount?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  network?: string;

  @Column({
    type: DataType.DECIMAL(65, 3),
    allowNull: false,
    defaultValue: 0,
  })
  milk: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: true,
  })
  isStar?: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: true,
  })
  hasCollectedFourChests?: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  lastActiveTime?: Date;

  // 关联关系
  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => UserWallet, 'referrerWalletId')
  referrerWallet: UserWallet;

  @HasMany(() => UserWallet, 'referrerWalletId')
  referredWallets: UserWallet[];
}
