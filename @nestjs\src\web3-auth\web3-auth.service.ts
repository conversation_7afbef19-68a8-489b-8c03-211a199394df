import { Injectable, Inject } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { GetNonceDto, NonceResponseDto } from './dto/get-nonce.dto';

@Injectable()
export class Web3AuthService {
  constructor(
    @Inject('REDIS_CLIENT') private readonly redisClient: any,
  ) {}

  /**
   * 生成用于签名的消息
   * @param nonce 随机数
   * @returns 签名消息
   */
  generateSignMessage(nonce: string): string {
    return `Welcome to MooFun!\n\nPlease sign to verify your wallet address, after which you can begin your wonderful journey.\n\nSecurity code: ${nonce}\nTimestamp: ${Date.now()}`;
  }

  /**
   * 获取nonce
   * @param getNonceDto 请求参数
   * @returns nonce和签名消息
   */
  async getNonce(getNonceDto: GetNonceDto): Promise<NonceResponseDto> {
    const { walletAddress } = getNonceDto;
    
    // 生成随机nonce
    const nonce = uuidv4();
    
    // 将nonce存储到Redis，设置5分钟过期
    const key = `web3_auth_nonce:${walletAddress.toLowerCase()}`;
    await this.redisClient.set(key, nonce, 'EX', 300);
    
    // 生成签名消息
    const message = this.generateSignMessage(nonce);
    
    return {
      nonce,
      message,
    };
  }
}
