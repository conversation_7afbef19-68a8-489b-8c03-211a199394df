import { Injectable, Inject, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { GetNonceDto, NonceResponseDto } from './dto/get-nonce.dto';

@Injectable()
export class Web3AuthService {
  private readonly logger = new Logger(Web3AuthService.name);

  constructor(
    @Inject('REDIS_CLIENT') private readonly redisClient: any,
  ) {}

  /**
   * 生成用于签名的消息
   * @param nonce 随机数
   * @returns 签名消息
   */
  generateSignMessage(nonce: string): string {
    return `Welcome to MooFun!\n\nPlease sign to verify your wallet address, after which you can begin your wonderful journey.\n\nSecurity code: ${nonce}\nTimestamp: ${Date.now()}`;
  }

  /**
   * 检查Redis连接状态
   * @returns Redis连接是否正常
   */
  async checkRedisConnection(): Promise<boolean> {
    const startTime = Date.now();

    try {
      const testKey = 'web3_auth_health_check';
      const testValue = 'ok';

      // 测试写入
      await this.redisClient.set(testKey, testValue, 'EX', 10);

      // 测试读取
      const result = await this.redisClient.get(testKey);

      // 清理测试数据
      await this.redisClient.del(testKey);

      const duration = Date.now() - startTime;
      this.logger.debug(`Redis健康检查完成，耗时: ${duration}ms`);

      return result === testValue;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Redis连接检查失败，耗时: ${duration}ms`, error);
      return false;
    }
  }

  /**
   * 获取nonce
   * @param getNonceDto 请求参数
   * @returns nonce和签名消息
   * @throws Error 当Redis操作失败时
   */
  async getNonce(getNonceDto: GetNonceDto): Promise<NonceResponseDto> {
    const startTime = Date.now();
    const { walletAddress } = getNonceDto;

    // 验证钱包地址格式（基本验证）
    if (!walletAddress || typeof walletAddress !== 'string') {
      this.logger.warn(`无效的钱包地址格式: ${walletAddress}`);
      throw new Error('Invalid wallet address format');
    }

    // 生成随机nonce（性能优化：直接生成UUID，无需额外处理）
    const nonce = uuidv4();

    try {
      // 将nonce存储到Redis，设置5分钟过期
      // 使用与原版本完全相同的Redis命令格式
      const key = `web3_auth_nonce:${walletAddress.toLowerCase()}`;

      // 性能优化：使用Promise.all并行执行消息生成和Redis存储
      const [, message] = await Promise.all([
        this.redisClient.set(key, nonce, 'EX', 300),
        Promise.resolve(this.generateSignMessage(nonce))
      ]);

      const duration = Date.now() - startTime;
      this.logger.debug(`Nonce生成完成，钱包: ${walletAddress.substring(0, 10)}..., 耗时: ${duration}ms`);

      return {
        nonce,
        message,
      };
    } catch (redisError) {
      const duration = Date.now() - startTime;
      this.logger.error(`Redis存储nonce失败，钱包: ${walletAddress}, 耗时: ${duration}ms`, redisError);
      throw new Error('Failed to store nonce in Redis');
    }
  }
}
