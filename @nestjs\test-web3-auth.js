// 简单的测试脚本来验证Web3Auth接口
const axios = require('axios');

async function testWeb3AuthNonce() {
  try {
    console.log('测试 /api/web3-auth/nonce 接口...');
    
    // 测试正常请求
    const response = await axios.post('http://localhost:3001/api/web3-auth/nonce', {
      walletAddress: '******************************************'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh'
      }
    });
    
    console.log('✅ 正常请求成功:');
    console.log('状态码:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    // 验证响应格式
    const { data } = response;
    if (data.ok && data.data && data.data.nonce && data.data.message) {
      console.log('✅ 响应格式正确');
      console.log('Nonce:', data.data.nonce);
      console.log('Message preview:', data.data.message.substring(0, 50) + '...');
    } else {
      console.log('❌ 响应格式不正确');
    }
    
  } catch (error) {
    if (error.response) {
      console.log('❌ 请求失败:');
      console.log('状态码:', error.response.status);
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ 网络错误:', error.message);
    }
  }
  
  try {
    console.log('\n测试参数验证错误...');
    
    // 测试缺少参数的请求
    const response = await axios.post('http://localhost:3001/api/web3-auth/nonce', {
      // 缺少 walletAddress
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh'
      }
    });
    
    console.log('❌ 应该返回验证错误，但请求成功了');
    
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ 参数验证错误正确返回:');
      console.log('状态码:', error.response.status);
      console.log('错误响应:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ 参数验证错误处理不正确');
      console.log('状态码:', error.response?.status);
      console.log('错误响应:', JSON.stringify(error.response?.data, null, 2));
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testWeb3AuthNonce().catch(console.error);
}

module.exports = { testWeb3AuthNonce };
