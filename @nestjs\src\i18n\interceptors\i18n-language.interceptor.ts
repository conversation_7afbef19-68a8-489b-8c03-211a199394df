import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { I18nContext } from 'nestjs-i18n';
import { SUPPORTED_LANGUAGES, SupportedLanguage } from '../i18n.service';

@Injectable()
export class I18nLanguageInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    // 只从 Accept-Language 请求头获取语言设置
    const acceptLanguage = request.headers['accept-language'];

    // 设置默认语言
    let language: SupportedLanguage = 'en';

    try {
      if (acceptLanguage) {
        // 解析 Accept-Language 头
        const languages = acceptLanguage.split(',').map(lang => {
          const [code, quality = '1'] = lang.trim().split(';q=');
          return { code: code.toLowerCase(), quality: parseFloat(quality) };
        });

        // 按质量排序并选择第一个支持的语言
        languages.sort((a, b) => b.quality - a.quality);

        for (const lang of languages) {
          const langCode = lang.code.split('-')[0]; // 提取主要语言代码
          if (this.isValidLanguage(lang.code)) {
            language = lang.code as SupportedLanguage;
            break;
          } else if (this.isValidLanguage(langCode)) {
            language = langCode as SupportedLanguage;
            break;
          }
        }
      }
    } catch (error) {
      console.error('Language interceptor error:', error);
      // 发生错误时使用默认语言
      language = 'en';
    }

    // 将语言信息添加到请求对象（兼容现有系统）
    request.language = language;
    request.i18nLang = language;

    // 设置 I18n 上下文
    const i18nContext = I18nContext.current();
    if (i18nContext) {
      // 使用正确的方式设置语言
      (i18nContext as any).lang = language;
    }

    return next.handle();
  }

  private isValidLanguage(lang: string): boolean {
    return SUPPORTED_LANGUAGES.includes(lang as SupportedLanguage);
  }
}
