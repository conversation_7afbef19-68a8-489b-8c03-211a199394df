import { Module, Global } from '@nestjs/common';
import {
  I18nModule as NestI18nModule,
  AcceptLanguageResolver,
  QueryResolver,
  HeaderResolver,
  I18nJsonLoader
} from 'nestjs-i18n';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as path from 'path';
import { CustomI18nService } from './i18n.service';

@Global()
@Module({
  imports: [
    NestI18nModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const i18nConfig = configService.get('i18n') || {};
        return {
          fallbackLanguage: i18nConfig.fallbackLanguage || 'en',
          loader: I18nJsonLoader,
          loaderOptions: {
            path: path.join(__dirname, './'),
            watch: process.env.NODE_ENV === 'development',
          },
          resolvers: [
            // 按优先级顺序：查询参数 > Accept-Language 头
            { use: QueryResolver, options: ['lang', 'l'] },
            AcceptLanguageResolver,
          ],
          formatter: i18nConfig.formatter || 'icu',
          logging: process.env.NODE_ENV === 'development',
          throwOnMissingKey: process.env.NODE_ENV === 'development',
          typesOutputPath: i18nConfig.typesOutputPath || path.join(__dirname, '../generated/i18n.generated.ts'),
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [
    CustomI18nService,
  ],
  exports: [
    NestI18nModule,
    CustomI18nService,
  ],
})
export class I18nModule {}
