import { Module, Global } from '@nestjs/common';
import { I18nModule as NestI18nModule, HeaderResolver } from 'nestjs-i18n';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as path from 'path';
import { CustomI18nService } from './i18n.service';
import { I18nLanguageInterceptor } from './interceptors/i18n-language.interceptor';

@Global()
@Module({
  imports: [
    NestI18nModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const i18nConfig = configService.get('i18n');
        return {
          fallbackLanguage: i18nConfig.fallbackLanguage,
          loaderOptions: {
            path: path.join(__dirname, './'),
            watch: i18nConfig.loaderOptions.watch,
          },
          resolvers: [
            // 只使用 Accept-Language 请求头
            new HeaderResolver(['accept-language']),
          ],
          formatter: i18nConfig.formatter,
          logging: i18nConfig.logging,
          throwOnMissingKey: i18nConfig.throwOnMissingKey,
          typesOutputPath: i18nConfig.typesOutputPath,
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [
    CustomI18nService,
    I18nLanguageInterceptor,
  ],
  exports: [
    NestI18nModule,
    CustomI18nService,
    I18nLanguageInterceptor,
  ],
})
export class I18nModule {}
